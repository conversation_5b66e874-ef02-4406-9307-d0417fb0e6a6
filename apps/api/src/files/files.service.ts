import md5 from 'md5';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectS3, S3 } from 'nestjs-s3';
import { Readable } from 'stream';
import { UnknownFileExtensionException } from './files.exceptions';
import { FileData } from './file.interface';
import { CannotRemoveFileException, CannotUploadFileException } from './files.exceptions';

@Injectable()
export class FilesService {
	constructor(
		@InjectS3() private readonly s3: S3,
		private readonly configService: ConfigService
	) {}

	get s3BucketName() {
		return this.configService.get<string>('S3_BUCKET_NAME');
	}

	get s3Region() {
		return this.configService.get<string>('S3_REGION');
	}

	get s3PublicHost() {
		return this.configService.get<string>('S3_PUBLIC_HOST');
	}

	private generatePublicFileUrl(filePath: string) {
		return `https://${this.s3PublicHost ?? ''}/${filePath}`;
	}

	/**
	 * @throws {CannotUploadFileException}
	 */
	async upload(
		file: FileData,
		extra: {
			allowExtensions: RegExp;
			folder?: string;
		}
	) {
		try {
			const filePath = this.generateFilePath({
				fileName: file.name,
				mimeType: file.mimeType,
				allowExtensions: extra.allowExtensions,
				folder: extra.folder,
			});

			// await this.s3.putObject({
			// 	Bucket: this.s3BucketName,
			// 	Key: filePath,
			// 	ContentLength: file.size,
			// 	ContentType: file.mimeType,
			// 	Body: Readable.from(file.buffer),
			// });

			return this.generatePublicFileUrl(filePath);
		} catch (err) {
			throw new CannotUploadFileException({
				cause: err,
			});
		}
	}

	/**
	 * @throws {CannotRemoveFileException}
	 */
	async remove(fileUrl: string) {
		try {
			await this.s3.deleteObject({
				Bucket: this.s3BucketName,
				Key: this.getFilePathFromUrl(fileUrl),
			});
		} catch (err) {
			console.error(err);
			throw new CannotRemoveFileException({
				cause: err,
			});
		}
	}

	/**
	 * @throws {UnknownFileExtension}
	 */
	private generateFilePath(data: { fileName: string; allowExtensions: RegExp; mimeType: string; folder?: string }): string {
		const hash = md5(data.fileName);
		const isFileExtensionValid = data.allowExtensions.test(data.mimeType);

		if (!isFileExtensionValid) {
			throw new UnknownFileExtensionException(data.fileName);
		}

		let filePath = hash;
		if (data.folder) {
			filePath = data.folder + '/' + filePath;
		}

		return filePath;
	}

	private getFilePathFromUrl(url: string): string {
		const match = /(?<=\/)(files\/)?((\w+\/)*[a-f0-9]{32})$/.exec(url);
		if (!match) {
			throw new Error('Invalid file URL format');
		}
		return match[2];
	}
}
