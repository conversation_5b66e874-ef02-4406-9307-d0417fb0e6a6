import {
	BadRequestException,
	ClassSerializerInterceptor,
	Controller,
	Delete,
	FileTypeValidator,
	HttpException,
	HttpStatus,
	MaxFileSizeValidator,
	ParseFilePipe,
	Post,
	UploadedFile,
	UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {ApiConsumes, ApiBody, ApiOperation} from '@nestjs/swagger';

import { CannotRemoveAvatarException, CannotUploadAvatarException, NoAvatarException } from './user-avatars.exceptions';
import { UserAvatarEntity } from './user-avatar.entity';
import { UserAvatarsService } from './user-avatars.service';
import { IMAGE_EXTENSION_REGEX, MAX_IMAGE_SIZE_IN_MB } from '../common/constants';
import { User } from '../common/decorators';
import { AuthedUserGuard } from '../auth/guards';
import Authorized from '../common/decorators/authorized.decorator';
import { ClsService } from 'nestjs-cls';
import JwtType from '../common/types/jwt-type.enum';
import messages from '../common/types/messages';
import { joi2swagger } from '../common/utils/joi2swagger';

import { RequestUser } from '../common/typings';

@UseInterceptors(ClassSerializerInterceptor)
@Controller('users/me/avatars')
export class UserAvatarsController {
	constructor(
		private readonly userAvatarsService: UserAvatarsService,
		private readonly cls: ClsService
) {}

	@Post('/upload')
	@Authorized()
	@ApiConsumes('multipart/form-data')
	@ApiOperation({ summary: 'Upload user avatar' })
	@ApiBody({
		schema: {
			type: 'object',
			properties: {
				image: {
					type: 'string',
					format: 'binary',
				},
			},
		},
	})
	@UseInterceptors(FileInterceptor('image'))
	async upload(
		@UploadedFile(
			new ParseFilePipe({
				validators: [
					new FileTypeValidator({ fileType: IMAGE_EXTENSION_REGEX }),
					new MaxFileSizeValidator({
						maxSize: 1024 * 1024 * MAX_IMAGE_SIZE_IN_MB,
						message: 'file_too_big',
					}),
				],
			})
		)
		file: Express.Multer.File,
	) {
		try {
			const context = this.cls.get('context');

			if (context.key != JwtType.Access) {
				throw new BadRequestException(messages.NO_ACCESS);
			}

			const avatar = await this.userAvatarsService.upload(context.user.id, {
				buffer: file.buffer,
				name: String(context.user.id),
				size: file.size,
				mimeType: file.mimetype,
			});

			return new UserAvatarEntity(avatar);
		} catch (err) {
			if (err instanceof CannotUploadAvatarException) {
				throw new HttpException(err.code, HttpStatus.BAD_REQUEST);
			}

			throw err;
		}
	}

	@AuthedUserGuard
	@Delete()
	async remove(@User() user: RequestUser) {
		try {
			const avatar = await this.userAvatarsService.remove(user.id, user.avatar_url);
			return new UserAvatarEntity(avatar);
		} catch (err) {
			if (err instanceof CannotRemoveAvatarException || err instanceof NoAvatarException) {
				throw new HttpException(err.code, HttpStatus.BAD_REQUEST);
			}

			throw err;
		}
	}
}
