version: '3.9'
services:
    db:
        container_name: db
        image: postgres:15
        env_file:
            - .env
        restart: unless-stopped
        volumes:
            - postgres_db:/var/lib/postgresql/data
            - ./ci/postgresql.conf:/etc/postgresql.conf:ro
        ports: ['${POSTGRES_PORT}:${POSTGRES_PORT}']
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
            POSTGRES_DB: ${POSTGRES_DB}
            POSTGRES_PORT: ${POSTGRES_PORT}
        command:
            - 'postgres'
            - '-c'
            - 'config_file=/etc/postgresql.conf'
        healthcheck:
            test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB} -p ${POSTGRES_PORT}']
            interval: 10s
            timeout: 5s
            retries: 5
            start_period: 10s

    redis:
        env_file:
            - .env
        image: redis:latest
        restart: unless-stopped
        ports: ['${REDIS_PORT}:${REDIS_PORT}']
        command: >
            --requirepass ${REDIS_PASSWORD}
        volumes:
            - redis:/var/lib/redis
            - ./ci/redis.conf:/usr/local/etc/redis/redis.conf
        environment:
            REDIS_PORT: ${REDIS_PORT}
        healthcheck:
            test: ['CMD', 'redis-cli', 'ping']
            interval: 20s
            timeout: 5s
            retries: 5
            start_period: 10s

    pgadmin:
        image: dpage/pgadmin4
        restart: unless-stopped
        environment:
            PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
            PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
        ports:
            - '5050:80'
        depends_on:
            - db

    app:
        env_file:
            - .env
        restart: always
        build:
            context: .
            dockerfile: Dockerfile
        environment:
            - PORT=3000
            - DEBUG=true
            - NODE_ENV=development
            - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public
        ports:
            - '3000-3001:3000'
            - 9229:9229
        depends_on:
            db:
                condition: service_healthy
            redis:
                condition: service_healthy
        healthcheck:
            test: 'curl http://localhost:3000/health > /dev/null'
            interval: 1m
            timeout: 30s
            retries: 3
            start_period: 60s
        deploy:
            update_config:
                order: start-first
                failure_action: rollback
                delay: 5s
        volumes:
            - ./apps:/app/apps
            - ./libs:/app/libs
            - ./prisma:/app/prisma
            - ./src:/app/src
            - ./tsconfig.json:/app/tsconfig.json
            - ./nest-cli.json:/app/nest-cli.json
        entrypoint: sh -c "npm run build && npm run prisma:migrate && npm run start:debug"

    nginx:
        build:
            context: ci/nginx
        ports:
            - '80:80'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        healthcheck:
            test: 'service nginx status || exit 1'
            interval: 1m
            timeout: 30s
            retries: 3

volumes:
    postgres_db: null
    redis: null
